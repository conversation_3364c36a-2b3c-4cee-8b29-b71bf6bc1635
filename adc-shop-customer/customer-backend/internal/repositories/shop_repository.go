package repositories

import (
	"context"
	"customer-backend/internal/models"
	"customer-backend/internal/types"
	"customer-backend/pkg/logger"
	"encoding/json"
	"fmt"
	"strings"

	"github.com/google/uuid"
	"gorm.io/datatypes"
	"gorm.io/gorm"
)

// ShopRepository handles shop-related database operations for customers
type ShopRepository struct {
	db     *gorm.DB
	logger *logger.Logger
}

// NewShopRepository creates a new shop repository
func NewShopRepository(db *gorm.DB, logger *logger.Logger) *ShopRepository {
	return &ShopRepository{
		db:     db,
		logger: logger,
	}
}

// GetShopSettings retrieves shop settings optimized for customer view
func (r *ShopRepository) GetShopSettings(ctx context.Context, shopID uuid.UUID) (*types.CustomerShopSettings, error) {
	var shop struct {
		ID          uuid.UUID `gorm:"column:id"`
		Slug        string    `gorm:"column:slug"`
		Name        string    `gorm:"column:name"`
		Description string    `gorm:"column:description"`
		Logo        string    `gorm:"column:logo"`
		CoverImage  string    `gorm:"column:cover_image"`
		CuisineType string    `gorm:"column:cuisine_type"`
		PriceRange  string    `gorm:"column:price_range"`
		Rating      float64   `gorm:"column:rating"`
		ReviewCount int       `gorm:"column:review_count"`
		Phone       string    `gorm:"column:phone"`
		Email       string    `gorm:"column:email"`
		Website     string    `gorm:"column:website"`

		// Address fields (embedded with prefix)
		AddressStreet  string `gorm:"column:address_street"`
		AddressCity    string `gorm:"column:address_city"`
		AddressState   string `gorm:"column:address_state"`
		AddressZipCode string `gorm:"column:address_zip_code"`
		AddressCountry string `gorm:"column:address_country"`

		// Settings (stored as JSONB)
		Settings      map[string]interface{} `gorm:"column:settings;type:jsonb"`
		BusinessHours map[string]string      `gorm:"column:business_hours;type:jsonb"`
		SocialMedia   map[string]string      `gorm:"column:social_media;type:jsonb"`

		IsActive bool `gorm:"column:is_active"`
	}

	err := r.db.WithContext(ctx).
		Table("shops").
		Where("id = ? AND is_active = ?", shopID, true).
		First(&shop).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("shop not found")
		}
		return nil, fmt.Errorf("failed to get shop settings: %w", err)
	}

	// Convert to customer shop settings
	customerShop := &types.CustomerShopSettings{
		ID:          shop.ID,
		Slug:        shop.Slug,
		Name:        shop.Name,
		Description: shop.Description,
		Logo:        shop.Logo,
		CoverImage:  shop.CoverImage,
		CuisineType: shop.CuisineType,
		PriceRange:  shop.PriceRange,
		Rating:      shop.Rating,
		ReviewCount: shop.ReviewCount,
		Phone:       shop.Phone,
		Email:       shop.Email,
		Website:     shop.Website,
		Address: types.Address{
			Street:  shop.AddressStreet,
			City:    shop.AddressCity,
			State:   shop.AddressState,
			ZipCode: shop.AddressZipCode,
			Country: shop.AddressCountry,
			// Note: Location fields would need to be added to the struct above if needed
		},
		BusinessHours: shop.BusinessHours,
		IsActive:      shop.IsActive,
	}

	// Extract customer-relevant settings
	if shop.Settings != nil {
		customerShop.Theme = extractThemeSettings(shop.Settings)
		customerShop.Features = extractFeatureSettings(shop.Settings)
		customerShop.PaymentMethods = extractPaymentMethods(shop.Settings)
		customerShop.Currency = extractStringFromSettings(shop.Settings, "currency", "USD")
		customerShop.TaxRate = extractFloatFromSettings(shop.Settings, "tax_rate", 0.0)
		customerShop.ServiceChargeRate = extractFloatFromSettings(shop.Settings, "service_charge_rate", 0.0)
		customerShop.DefaultTipPercentage = extractFloatFromSettings(shop.Settings, "default_tip_percentage", 15.0)
	}

	// Extract social media
	if shop.SocialMedia != nil {
		customerShop.SocialMedia = types.SocialMediaLinks{
			Facebook:  shop.SocialMedia["facebook"],
			Instagram: shop.SocialMedia["instagram"],
			Twitter:   shop.SocialMedia["twitter"],
			LinkedIn:  shop.SocialMedia["linkedin"],
			TikTok:    shop.SocialMedia["tiktok"],
			YouTube:   shop.SocialMedia["youtube"],
		}
	}

	// Check if shop is currently open
	customerShop.IsOpen = r.isShopCurrentlyOpen(customerShop.BusinessHours)

	return customerShop, nil
}

// GetShopsByFilters retrieves shops based on customer filters
func (r *ShopRepository) GetShopsByFilters(ctx context.Context, filters types.ShopFilters) ([]types.CustomerShopSettings, int64, error) {
	query := r.db.WithContext(ctx).Model(&models.Shop{}).Where("is_active = ?", true)

	// Apply filters
	query = r.applyShopFilters(query, filters)

	// Count total records
	var total int64
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count shops: %w", err)
	}

	// Apply pagination and sorting
	query = r.applySortingAndPagination(query, filters.CustomerFilters)

	var shops []models.Shop
	err := query.Find(&shops).Error
	if err != nil {
		return nil, 0, fmt.Errorf("failed to get shops: %w", err)
	}

	// Convert to customer shop settings
	customerShops := make([]types.CustomerShopSettings, len(shops))
	for i, shop := range shops {
		customerShops[i] = r.convertToCustomerShopSettings(shop)
	}

	return customerShops, total, nil
}

// GetShopSettingsBySlug retrieves shop settings by slug for customer view
func (r *ShopRepository) GetShopSettingsBySlug(ctx context.Context, slug string) (*types.CustomerShopSettings, error) {
	var shop struct {
		ID          uuid.UUID `gorm:"column:id"`
		Slug        string    `gorm:"column:slug"`
		Name        string    `gorm:"column:name"`
		Description string    `gorm:"column:description"`
		Logo        string    `gorm:"column:logo"`
		CoverImage  string    `gorm:"column:cover_image"`
		CuisineType string    `gorm:"column:cuisine_type"`
		PriceRange  string    `gorm:"column:price_range"`
		Rating      float64   `gorm:"column:rating"`
		ReviewCount int       `gorm:"column:review_count"`
		Phone       string    `gorm:"column:phone"`
		Email       string    `gorm:"column:email"`
		Website     string    `gorm:"column:website"`

		// Address fields (embedded with prefix)
		AddressStreet  string `gorm:"column:address_street"`
		AddressCity    string `gorm:"column:address_city"`
		AddressState   string `gorm:"column:address_state"`
		AddressZipCode string `gorm:"column:address_zip_code"`
		AddressCountry string `gorm:"column:address_country"`

		// Settings (stored as JSONB)
		Settings      datatypes.JSON `gorm:"column:settings;type:jsonb"`
		BusinessHours datatypes.JSON `gorm:"column:business_hours;type:jsonb"`
		SocialMedia   datatypes.JSON `gorm:"column:social_media;type:jsonb"`

		IsActive bool `gorm:"column:is_active"`
	}

	err := r.db.WithContext(ctx).
		Table("shops").
		Where("slug = ? AND is_active = ?", slug, true).
		First(&shop).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("shop not found")
		}
		return nil, fmt.Errorf("failed to get shop settings: %w", err)
	}

	// Convert to customer shop settings
	customerShop := &types.CustomerShopSettings{
		ID:          shop.ID,
		Slug:        shop.Slug,
		Name:        shop.Name,
		Description: shop.Description,
		Logo:        shop.Logo,
		CoverImage:  shop.CoverImage,
		CuisineType: shop.CuisineType,
		PriceRange:  shop.PriceRange,
		Rating:      shop.Rating,
		ReviewCount: shop.ReviewCount,
		Phone:       shop.Phone,
		Email:       shop.Email,
		Website:     shop.Website,
		Address: types.Address{
			Street:  shop.AddressStreet,
			City:    shop.AddressCity,
			State:   shop.AddressState,
			ZipCode: shop.AddressZipCode,
			Country: shop.AddressCountry,
		},
		IsActive: shop.IsActive,
	}

	// Extract customer-relevant settings
	if len(shop.Settings) > 0 {
		var settingsMap map[string]interface{}
		if err := json.Unmarshal(shop.Settings, &settingsMap); err == nil {
			customerShop.Theme = extractThemeSettings(settingsMap)
			customerShop.Features = extractFeatureSettings(settingsMap)
			customerShop.PaymentMethods = extractPaymentMethods(settingsMap)
			customerShop.Currency = extractStringFromSettings(settingsMap, "currency", "USD")
			customerShop.TaxRate = extractFloatFromSettings(settingsMap, "tax_rate", 0.0)
			customerShop.ServiceChargeRate = extractFloatFromSettings(settingsMap, "service_charge_rate", 0.0)
			customerShop.DefaultTipPercentage = extractFloatFromSettings(settingsMap, "default_tip_percentage", 15.0)
		}
	}

	// Extract business hours
	if len(shop.BusinessHours) > 0 {
		var businessHoursMap map[string]string
		if err := json.Unmarshal(shop.BusinessHours, &businessHoursMap); err == nil {
			customerShop.BusinessHours = businessHoursMap
		}
	}

	// Extract social media
	if len(shop.SocialMedia) > 0 {
		var socialMediaMap map[string]string
		if err := json.Unmarshal(shop.SocialMedia, &socialMediaMap); err == nil {
			customerShop.SocialMedia = types.SocialMediaLinks{
				Facebook:  socialMediaMap["facebook"],
				Instagram: socialMediaMap["instagram"],
				Twitter:   socialMediaMap["twitter"],
				LinkedIn:  socialMediaMap["linkedin"],
				TikTok:    socialMediaMap["tiktok"],
				YouTube:   socialMediaMap["youtube"],
			}
		}
	}

	// Check if shop is currently open
	customerShop.IsOpen = r.isShopCurrentlyOpen(customerShop.BusinessHours)

	return customerShop, nil
}

// Helper methods

func (r *ShopRepository) applyShopFilters(query *gorm.DB, filters types.ShopFilters) *gorm.DB {
	// Debug logging
	r.logger.WithFields(map[string]any{
		"search":       filters.Search,
		"cuisine_type": filters.CuisineType,
		"price_range":  filters.PriceRange,
		"min_rating":   filters.MinRating,
	}).Info("Applying shop filters to query")

	// Search filter
	if filters.Search != "" {
		searchPattern := "%" + filters.Search + "%"
		query = query.Where("(name ILIKE ? OR description ILIKE ? OR cuisine_type ILIKE ?)",
			searchPattern, searchPattern, searchPattern)
		r.logger.WithField("search_pattern", searchPattern).Info("Applied search filter")
	}

	// Cuisine type filter
	if len(filters.CuisineType) > 0 {
		query = query.Where("cuisine_type IN ?", filters.CuisineType)
		r.logger.WithField("cuisine_types", filters.CuisineType).Info("Applied cuisine type filter")
	}

	// Price range filter
	if len(filters.PriceRange) > 0 {
		query = query.Where("price_range IN ?", filters.PriceRange)
		r.logger.WithField("price_ranges", filters.PriceRange).Info("Applied price range filter")
	}

	// Rating filter
	if filters.MinRating != nil {
		query = query.Where("rating >= ?", *filters.MinRating)
		r.logger.WithField("min_rating", *filters.MinRating).Info("Applied rating filter")
	}

	// Location-based filter (if coordinates provided)
	if filters.Latitude != nil && filters.Longitude != nil && filters.Radius != nil {
		// Using Haversine formula for distance calculation
		query = query.Where(`
			(6371 * acos(cos(radians(?)) * cos(radians(address_latitude)) *
			cos(radians(address_longitude) - radians(?)) +
			sin(radians(?)) * sin(radians(address_latitude)))) <= ?`,
			*filters.Latitude, *filters.Longitude, *filters.Latitude, *filters.Radius)
	}

	return query
}

func (r *ShopRepository) applySortingAndPagination(query *gorm.DB, filters types.CustomerFilters) *gorm.DB {
	// Apply sorting
	sortBy := filters.SortBy
	if sortBy == "" {
		sortBy = "rating"
	}

	sortOrder := strings.ToUpper(filters.SortOrder)
	if sortOrder != "ASC" && sortOrder != "DESC" {
		sortOrder = "DESC"
	}

	query = query.Order(fmt.Sprintf("%s %s", sortBy, sortOrder))

	// Apply pagination
	if filters.Page > 0 && filters.Limit > 0 {
		offset := (filters.Page - 1) * filters.Limit
		query = query.Offset(offset).Limit(filters.Limit)
	}

	return query
}

func (r *ShopRepository) isShopCurrentlyOpen(businessHours map[string]string) bool {
	// This is a simplified implementation
	// In production, you'd want to check against current time and timezone
	return len(businessHours) > 0
}

// Helper functions to extract settings

func extractThemeSettings(settings map[string]interface{}) types.CustomerTheme {
	theme := types.CustomerTheme{}

	if themeMap, ok := settings["theme"].(map[string]interface{}); ok {
		if primary, ok := themeMap["primary_color"].(string); ok {
			theme.PrimaryColor = primary
		}
		if secondary, ok := themeMap["secondary_color"].(string); ok {
			theme.SecondaryColor = secondary
		}
		if accent, ok := themeMap["accent_color"].(string); ok {
			theme.AccentColor = accent
		}
	}

	return theme
}

func extractFeatureSettings(settings map[string]interface{}) types.CustomerFeatures {
	features := types.CustomerFeatures{}

	if featuresMap, ok := settings["features"].(map[string]interface{}); ok {
		if onlineOrdering, ok := featuresMap["online_ordering"].(bool); ok {
			features.OnlineOrdering = onlineOrdering
		}
		if tableReservations, ok := featuresMap["table_reservations"].(bool); ok {
			features.TableReservations = tableReservations
		}
		if qrMenu, ok := featuresMap["qr_menu"].(bool); ok {
			features.QRMenu = qrMenu
		}
		if reviews, ok := featuresMap["reviews"].(bool); ok {
			features.Reviews = reviews
		}
	}

	return features
}

func extractPaymentMethods(settings map[string]interface{}) []string {
	if methods, ok := settings["payment_methods"].([]interface{}); ok {
		result := make([]string, len(methods))
		for i, method := range methods {
			if str, ok := method.(string); ok {
				result[i] = str
			}
		}
		return result
	}
	return []string{}
}

func extractStringFromSettings(settings map[string]interface{}, key, defaultValue string) string {
	if value, ok := settings[key].(string); ok {
		return value
	}
	return defaultValue
}

func extractFloatFromSettings(settings map[string]interface{}, key string, defaultValue float64) float64 {
	if value, ok := settings[key].(float64); ok {
		return value
	}
	return defaultValue
}

// GetAvailableCuisineTypes retrieves all unique cuisine types from active shops
func (r *ShopRepository) GetAvailableCuisineTypes(ctx context.Context) ([]string, error) {
	var cuisineTypes []string

	err := r.db.WithContext(ctx).
		Model(&models.Shop{}).
		Where("is_active = ?", true).
		Distinct("cuisine_type").
		Pluck("cuisine_type", &cuisineTypes).Error

	if err != nil {
		return nil, fmt.Errorf("failed to get cuisine types: %w", err)
	}

	// Filter out empty strings
	var filteredTypes []string
	for _, cuisineType := range cuisineTypes {
		if cuisineType != "" {
			filteredTypes = append(filteredTypes, cuisineType)
		}
	}

	return filteredTypes, nil
}

// GetAvailablePriceRanges retrieves all unique price ranges from active shops
func (r *ShopRepository) GetAvailablePriceRanges(ctx context.Context) ([]string, error) {
	var priceRanges []string

	err := r.db.WithContext(ctx).
		Model(&models.Shop{}).
		Where("is_active = ?", true).
		Distinct("price_range").
		Pluck("price_range", &priceRanges).Error

	if err != nil {
		return nil, fmt.Errorf("failed to get price ranges: %w", err)
	}

	// Filter out empty strings
	var filteredRanges []string
	for _, priceRange := range priceRanges {
		if priceRange != "" {
			filteredRanges = append(filteredRanges, priceRange)
		}
	}

	return filteredRanges, nil
}

// GetBranchBySlug retrieves a branch by shop slug and branch slug
func (r *ShopRepository) GetBranchBySlug(ctx context.Context, shopSlug, branchSlug string) (*models.ShopBranch, error) {
	var branch models.ShopBranch
	err := r.db.WithContext(ctx).
		Joins("JOIN shops ON shop_branches.shop_id = shops.id").
		Where("shops.slug = ? AND shop_branches.slug = ? AND shops.is_active = ? AND shop_branches.is_active = ?",
			shopSlug, branchSlug, true, true).
		First(&branch).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("branch not found")
		}
		return nil, fmt.Errorf("failed to get branch: %w", err)
	}

	return &branch, nil
}

// convertToCustomerShopSettings converts a Shop model to CustomerShopSettings
func (r *ShopRepository) convertToCustomerShopSettings(shop models.Shop) types.CustomerShopSettings {
	customerShop := types.CustomerShopSettings{
		ID:          shop.ID,
		Slug:        shop.Slug,
		Name:        shop.Name,
		Description: shop.Description,
		Logo:        shop.Logo,
		CoverImage:  shop.CoverImage,
		CuisineType: shop.CuisineType,
		PriceRange:  shop.PriceRange,
		Rating:      shop.Rating,
		ReviewCount: shop.ReviewCount,
		Phone:       shop.Phone,
		Email:       shop.Email,
		Website:     shop.Website,
		Address: types.Address{
			Street:           shop.Address.Street,
			City:             shop.Address.City,
			State:            shop.Address.State,
			ZipCode:          shop.Address.ZipCode,
			Country:          shop.Address.Country,
			Latitude:         shop.Latitude,         // Use direct fields from Shop model
			Longitude:        shop.Longitude,        // Use direct fields from Shop model
			LocationAccuracy: shop.LocationAccuracy, // Use direct fields from Shop model
			GeocodedAt:       shop.GeocodedAt,       // Use direct fields from Shop model
			PlaceID:          shop.PlaceID,          // Use direct fields from Shop model
			FormattedAddress: shop.FormattedAddress, // Use direct fields from Shop model
			LocationType:     shop.LocationType,     // Use direct fields from Shop model
		},
		BusinessHours: types.BusinessHours(shop.BusinessHours),
		IsActive:      shop.IsActive,
		UpdatedAt:     shop.UpdatedAt,
	}

	// Extract customer-relevant settings
	customerShop.Currency = shop.Settings.Currency
	customerShop.TaxRate = shop.Settings.TaxRate
	customerShop.ServiceChargeRate = shop.Settings.ServiceChargeRate
	customerShop.DefaultTipPercentage = shop.Settings.DefaultTipPercentage
	customerShop.PaymentMethods = shop.Settings.PaymentMethods

	// Extract theme settings
	if shop.Settings.Theme != nil {
		customerShop.Theme = types.CustomerTheme{
			PrimaryColor:   shop.Settings.Theme["primary_color"],
			SecondaryColor: shop.Settings.Theme["secondary_color"],
			AccentColor:    shop.Settings.Theme["accent_color"],
		}
	}

	// Extract feature settings
	if shop.Settings.Features != nil {
		customerShop.Features = types.CustomerFeatures{
			OnlineOrdering:    shop.Settings.Features["online_ordering"],
			TableReservations: shop.Settings.Features["table_reservations"],
			QRMenu:            shop.Settings.Features["qr_menu"],
			Reviews:           shop.Settings.Features["reviews"],
			DeliveryEnabled:   shop.Settings.Features["delivery_enabled"],
			PickupEnabled:     shop.Settings.Features["pickup_enabled"],
		}
	}

	// Extract social media
	customerShop.SocialMedia = types.SocialMediaLinks{
		Facebook:  shop.SocialMedia.Facebook,
		Instagram: shop.SocialMedia.Instagram,
		Twitter:   shop.SocialMedia.Twitter,
		LinkedIn:  "", // Not available in models.SocialMediaLinks
		YouTube:   shop.SocialMedia.YouTube,
		TikTok:    shop.SocialMedia.TikTok,
	}

	// Check if shop is currently open
	customerShop.IsOpen = r.isShopCurrentlyOpen(map[string]string(shop.BusinessHours))

	return customerShop
}
