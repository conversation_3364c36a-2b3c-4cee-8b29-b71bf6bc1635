package repositories

import (
	"context"
	"customer-backend/internal/types"
	"customer-backend/pkg/logger"
	"encoding/json"
	"fmt"
	"strings"

	"github.com/google/uuid"
	"gorm.io/datatypes"
	"gorm.io/gorm"
)

// MenuRepository handles menu-related database operations for customers
type MenuRepository struct {
	db     *gorm.DB
	logger *logger.Logger
}

// NewMenuRepository creates a new menu repository
func NewMenuRepository(db *gorm.DB, logger *logger.Logger) *MenuRepository {
	return &MenuRepository{
		db:     db,
		logger: logger,
	}
}

// GetMenuItems retrieves menu items based on customer filters
func (r *MenuRepository) GetMenuItems(ctx context.Context, shopID uuid.UUID, filters types.MenuFilters) ([]types.CustomerMenuItem, int64, error) {
	// First, get the branch ID for the shop (assuming we're using the first branch)
	var branchIDStr string
	err := r.db.WithContext(ctx).
		Table("shop_branches").
		Select("id").
		Where("shop_id = ? AND is_active = ?", shopID, true).
		Scan(&branchIDStr).Error

	if err != nil {
		return nil, 0, fmt.Errorf("failed to find branch for shop: %w", err)
	}

	branchID, err := uuid.Parse(branchIDStr)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to parse branch ID: %w", err)
	}

	query := r.db.WithContext(ctx).
		Table("menu_items").
		Where("menu_items.branch_id = ? AND menu_items.is_available = ?", branchID, true)

	// Apply filters
	query = r.applyMenuFilters(query, filters)

	// Count total records
	var total int64
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count menu items: %w", err)
	}

	// Apply sorting and pagination
	query = r.applyMenuSortingAndPagination(query, filters.CustomerFilters)

	var items []struct {
		ID          uuid.UUID      `gorm:"column:id"`
		Name        string         `gorm:"column:name"`
		Description string         `gorm:"column:description"`
		Price       float64        `gorm:"column:price"`
		Images      datatypes.JSON `gorm:"column:images;type:jsonb"`
		IsAvailable bool           `gorm:"column:is_available"`

		// Dietary information
		IsVegetarian bool `gorm:"column:is_vegetarian"`
		IsVegan      bool `gorm:"column:is_vegan"`
		IsGlutenFree bool `gorm:"column:is_gluten_free"`
		IsSpicy      bool `gorm:"column:is_spicy"`
		SpiceLevel   int  `gorm:"column:spice_level"`

		// Nutritional info
		PreparationTime *int `gorm:"column:preparation_time"`

		// Tags and allergens (stored as JSONB)
		Tags      datatypes.JSON `gorm:"column:tags;type:jsonb"`
		Allergens datatypes.JSON `gorm:"column:allergens;type:jsonb"`

		// Category info
		CategoryID   *uuid.UUID `gorm:"column:category_id"`
		CategoryName string     `gorm:"column:category_name"`
	}

	err = query.
		Select(`
			menu_items.id,
			menu_items.name,
			menu_items.description,
			menu_items.price,
			menu_items.images,
			menu_items.is_available,
			menu_items.is_vegetarian,
			menu_items.is_vegan,
			menu_items.is_gluten_free,
			menu_items.is_spicy,
			menu_items.spice_level,
			menu_items.preparation_time,
			menu_items.tags,
			menu_items.allergens,
			menu_items.category_id,
			COALESCE(menu_categories.name, 'Uncategorized') as category_name
		`).
		Joins("LEFT JOIN menu_categories ON menu_items.category_id = menu_categories.id").
		Find(&items).Error
	if err != nil {
		return nil, 0, fmt.Errorf("failed to get menu items: %w", err)
	}

	// Convert to customer menu items
	customerItems := make([]types.CustomerMenuItem, len(items))
	for i, item := range items {
		// Parse images from JSONB
		var images []string
		if len(item.Images) > 0 {
			if err := json.Unmarshal(item.Images, &images); err != nil {
				images = []string{} // Default to empty if parsing fails
			}
		}

		// Get the first image or use a default
		var image string
		if len(images) > 0 {
			image = images[0]
		} else {
			image = "/placeholder-food.jpg"
		}

		// Parse tags from JSONB
		var tags []string
		if len(item.Tags) > 0 {
			if err := json.Unmarshal(item.Tags, &tags); err != nil {
				tags = []string{} // Default to empty if parsing fails
			}
		}

		// Parse allergens from JSONB
		var allergens []string
		if len(item.Allergens) > 0 {
			if err := json.Unmarshal(item.Allergens, &allergens); err != nil {
				allergens = []string{} // Default to empty if parsing fails
			}
		}

		customerItems[i] = types.CustomerMenuItem{
			ID:              item.ID.String(),
			Name:            item.Name,
			Description:     item.Description,
			Price:           item.Price,
			Image:           image,
			Category:        item.CategoryName,
			IsAvailable:     item.IsAvailable,
			IsPopular:       false, // Will be determined by business logic
			IsNew:           false, // Will be determined by business logic
			IsVegetarian:    item.IsVegetarian,
			IsVegan:         item.IsVegan,
			IsGlutenFree:    item.IsGlutenFree,
			IsSpicy:         item.IsSpicy,
			SpicyLevel:      item.SpiceLevel,
			PreparationTime: item.PreparationTime,
			Tags:            tags,
			Allergens:       allergens,
			Rating:          0.0, // Will be calculated from reviews
			ReviewCount:     0,   // Will be calculated from reviews
		}

		// Load customizations from JSONB options field
		customizations, hasCustomizations := r.parseCustomizationsFromOptions(ctx, item.ID)
		if hasCustomizations {
			customerItems[i].HasCustomizations = true
			customerItems[i].Customizations = customizations
		}
	}

	return customerItems, total, nil
}

// GetMenuItemsBySlug retrieves menu items based on customer filters using shop slug
func (r *MenuRepository) GetMenuItemsBySlug(ctx context.Context, shopSlug string, filters types.MenuFilters) ([]types.CustomerMenuItem, int64, error) {
	// First, get the shop ID using the slug
	var shopIDStr string
	err := r.db.WithContext(ctx).
		Table("shops").
		Select("id").
		Where("slug = ? AND is_active = ?", shopSlug, true).
		Scan(&shopIDStr).Error

	if err != nil {
		return nil, 0, fmt.Errorf("failed to find shop with slug %s: %w", shopSlug, err)
	}

	shopID, err := uuid.Parse(shopIDStr)

	if err != nil {
		return nil, 0, fmt.Errorf("failed to find shop with slug %s: %w", shopSlug, err)
	}

	// Now use the existing GetMenuItems method
	return r.GetMenuItems(ctx, shopID, filters)
}

// GetMenuCategoriesBySlug retrieves menu categories for a shop using slug
func (r *MenuRepository) GetMenuCategoriesBySlug(ctx context.Context, shopSlug string) ([]types.MenuCategory, error) {
	// First, get the shop ID using the slug
	var shopIDStr string
	err := r.db.WithContext(ctx).
		Table("shops").
		Select("id").
		Where("slug = ? AND is_active = ?", shopSlug, true).
		Scan(&shopIDStr).Error

	if err != nil {
		return nil, fmt.Errorf("failed to find shop with slug %s: %w", shopSlug, err)
	}

	shopID, err := uuid.Parse(shopIDStr)

	if err != nil {
		return nil, fmt.Errorf("failed to find shop with slug %s: %w", shopSlug, err)
	}

	// Now use the existing GetMenuCategories method
	return r.GetMenuCategories(ctx, shopID)
}

// GetMenuItemsByBranchSlug retrieves menu items based on customer filters using shop slug and branch slug
func (r *MenuRepository) GetMenuItemsByBranchSlug(ctx context.Context, shopSlug, branchSlug string, filters types.MenuFilters) ([]types.CustomerMenuItem, int64, error) {
	// First, get the branch ID using both shop slug and branch slug
	var branchIDStr string

	// Debug: Log the query parameters
	r.logger.Infof("Looking for branch with shop slug: %s, branch slug: %s", shopSlug, branchSlug)

	err := r.db.WithContext(ctx).
		Table("shop_branches").
		Select("shop_branches.id").
		Joins("JOIN shops ON shop_branches.shop_id = shops.id").
		Where("shops.slug = ? AND shop_branches.slug = ? AND shops.is_active = ? AND shop_branches.is_active = ?",
			shopSlug, branchSlug, true, true).
		Scan(&branchIDStr).Error

	if err != nil {
		// Debug: Check if it's a "no rows" error vs actual error
		if err == gorm.ErrRecordNotFound {
			r.logger.Errorf("No branch found with shop slug %s and branch slug %s", shopSlug, branchSlug)

			// Debug: Check what shops exist
			var shops []struct {
				Slug string `json:"slug"`
				Name string `json:"name"`
			}
			r.db.WithContext(ctx).Table("shops").Select("slug, name").Where("is_active = ?", true).Find(&shops)
			r.logger.Infof("Available shops: %+v", shops)

			// Debug: Check what branches exist for this shop
			var branches []struct {
				Slug string `json:"slug"`
				Name string `json:"name"`
			}
			r.db.WithContext(ctx).
				Table("shop_branches").
				Select("shop_branches.slug, shop_branches.name").
				Joins("JOIN shops ON shop_branches.shop_id = shops.id").
				Where("shops.slug = ? AND shop_branches.is_active = ?", shopSlug, true).
				Find(&branches)
			r.logger.Infof("Available branches for shop %s: %+v", shopSlug, branches)

			return nil, 0, fmt.Errorf("branch not found: shop_slug=%s, branch_slug=%s", shopSlug, branchSlug)
		}
		return nil, 0, fmt.Errorf("failed to find branch with shop slug %s and branch slug %s: %w", shopSlug, branchSlug, err)
	}

	branchID, err := uuid.Parse(branchIDStr)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to parse branch ID: %w", err)
	}

	// Now get menu items for this specific branch
	query := r.db.WithContext(ctx).
		Table("menu_items").
		Select(`
			menu_items.id,
			menu_items.name,
			menu_items.description,
			menu_items.price,
			menu_items.image,
			menu_items.category,
			menu_items.is_available,
			menu_items.is_popular,
			menu_items.is_new,
			menu_items.is_vegetarian,
			menu_items.is_vegan,
			menu_items.is_gluten_free,
			menu_items.is_spicy,
			menu_items.spicy_level,
			menu_items.calories,
			menu_items.preparation_time,
			menu_items.tags,
			menu_items.allergens,
			menu_items.rating,
			menu_items.review_count,
			menu_items.created_at,
			menu_items.updated_at
		`).
		Where("menu_items.branch_id = ? AND menu_items.is_active = ?", branchID, true)

	// Apply filters
	query = r.applyMenuFilters(query, filters)

	// Count total records before pagination
	var total int64
	countQuery := r.db.WithContext(ctx).
		Table("menu_items").
		Where("branch_id = ? AND is_active = ?", branchID, true)
	countQuery = r.applyMenuFilters(countQuery, filters)
	if err := countQuery.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count menu items: %w", err)
	}

	// Apply sorting and pagination
	query = r.applyMenuSortingAndPagination(query, filters.CustomerFilters)

	var items []types.CustomerMenuItem
	err = query.Find(&items).Error
	if err != nil {
		return nil, 0, fmt.Errorf("failed to get menu items: %w", err)
	}

	// Load customizations for items that have them
	for i := range items {
		itemID, err := uuid.Parse(items[i].ID)
		if err != nil {
			r.logger.Error("Failed to parse item ID", "error", err, "item_id", items[i].ID)
			continue
		}
		customizations, hasCustomizations := r.parseCustomizationsFromOptions(ctx, itemID)
		if hasCustomizations {
			items[i].HasCustomizations = true
			items[i].Customizations = customizations
		}
	}

	return items, total, nil
}

// GetMenuCategoriesByBranchSlug retrieves menu categories for a shop using shop slug and branch slug
func (r *MenuRepository) GetMenuCategoriesByBranchSlug(ctx context.Context, shopSlug, branchSlug string) ([]types.MenuCategory, error) {
	// First, get the branch ID using both shop slug and branch slug
	var branchIDStr string
	err := r.db.WithContext(ctx).
		Table("shop_branches").
		Select("shop_branches.id").
		Joins("JOIN shops ON shop_branches.shop_id = shops.id").
		Where("shops.slug = ? AND shop_branches.slug = ? AND shops.is_active = ? AND shop_branches.is_active = ?",
			shopSlug, branchSlug, true, true).
		Scan(&branchIDStr).Error

	if err != nil {
		return nil, fmt.Errorf("failed to find branch with shop slug %s and branch slug %s: %w", shopSlug, branchSlug, err)
	}

	branchID, err := uuid.Parse(branchIDStr)
	if err != nil {
		return nil, fmt.Errorf("failed to parse branch ID: %w", err)
	}

	var categories []types.MenuCategory

	err = r.db.WithContext(ctx).
		Table("menu_categories").
		Where("branch_id = ? AND is_active = ?", branchID, true).
		Order("sort_order ASC, name ASC").
		Find(&categories).Error

	if err != nil {
		return nil, fmt.Errorf("failed to get menu categories: %w", err)
	}

	// Count items in each category
	for i := range categories {
		var count int64
		r.db.WithContext(ctx).
			Table("menu_items").
			Where("category_id = ? AND is_available = ?", categories[i].ID, true).
			Count(&count)
		categories[i].ItemCount = int(count)
	}

	return categories, nil
}

// GetMenuItem retrieves a single menu item by ID
func (r *MenuRepository) GetMenuItem(ctx context.Context, itemID uuid.UUID) (*types.CustomerMenuItem, error) {
	var item types.CustomerMenuItem

	err := r.db.WithContext(ctx).
		Table("menu_items").
		Where("id = ? AND is_active = ?", itemID, true).
		First(&item).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("menu item not found")
		}
		return nil, fmt.Errorf("failed to get menu item: %w", err)
	}

	// Load customizations from JSONB options field
	customizations, hasCustomizations := r.parseCustomizationsFromOptions(ctx, itemID)
	if hasCustomizations {
		item.HasCustomizations = true
		item.Customizations = customizations
	}

	return &item, nil
}

// GetMenuCategories retrieves menu categories for a shop
func (r *MenuRepository) GetMenuCategories(ctx context.Context, shopID uuid.UUID) ([]types.MenuCategory, error) {
	// First, get the branch ID for the shop
	var branchIDStr string
	err := r.db.WithContext(ctx).
		Table("shop_branches").
		Select("id").
		Where("shop_id = ? AND is_active = ?", shopID, true).
		Scan(&branchIDStr).Error

	if err != nil {
		return nil, fmt.Errorf("failed to find branch for shop: %w", err)
	}

	branchID, err := uuid.Parse(branchIDStr)
	if err != nil {
		return nil, fmt.Errorf("failed to parse branch ID: %w", err)
	}

	var categories []types.MenuCategory

	err = r.db.WithContext(ctx).
		Table("menu_categories").
		Where("branch_id = ? AND is_active = ?", branchID, true).
		Order("sort_order ASC, name ASC").
		Find(&categories).Error

	if err != nil {
		return nil, fmt.Errorf("failed to get menu categories: %w", err)
	}

	// Count items in each category
	for i := range categories {
		var count int64
		r.db.WithContext(ctx).
			Table("menu_items").
			Where("category_id = ? AND is_available = ?", categories[i].ID, true).
			Count(&count)
		categories[i].ItemCount = int(count)
	}

	return categories, nil
}

// Helper methods

func (r *MenuRepository) applyMenuFilters(query *gorm.DB, filters types.MenuFilters) *gorm.DB {
	// Search filter
	if filters.Search != "" {
		searchPattern := "%" + filters.Search + "%"
		query = query.Where("(name ILIKE ? OR description ILIKE ?)", searchPattern, searchPattern)
	}

	// Category filter
	if filters.CategoryID != nil {
		query = query.Where("category_id = ?", *filters.CategoryID)
	}

	if len(filters.Categories) > 0 {
		query = query.Where("category IN ?", filters.Categories)
	}

	// Availability filter
	if filters.IsAvailable != nil {
		query = query.Where("is_available = ?", *filters.IsAvailable)
	}

	// Popular items filter
	if filters.IsPopular != nil {
		query = query.Where("is_popular = ?", *filters.IsPopular)
	}

	// New items filter
	if filters.IsNew != nil {
		query = query.Where("is_new = ?", *filters.IsNew)
	}

	// Dietary filters
	if filters.IsVegetarian != nil {
		query = query.Where("is_vegetarian = ?", *filters.IsVegetarian)
	}

	if filters.IsVegan != nil {
		query = query.Where("is_vegan = ?", *filters.IsVegan)
	}

	if filters.IsGlutenFree != nil {
		query = query.Where("is_gluten_free = ?", *filters.IsGlutenFree)
	}

	if filters.IsSpicy != nil {
		query = query.Where("is_spicy = ?", *filters.IsSpicy)
	}

	if filters.MaxSpicyLevel != nil {
		query = query.Where("spicy_level <= ?", *filters.MaxSpicyLevel)
	}

	// Price range filter
	if filters.PriceMin != nil {
		query = query.Where("price >= ?", *filters.PriceMin)
	}

	if filters.PriceMax != nil {
		query = query.Where("price <= ?", *filters.PriceMax)
	}

	// Nutritional filters
	if filters.MaxCalories != nil {
		query = query.Where("calories <= ?", *filters.MaxCalories)
	}

	if filters.MaxPrepTime != nil {
		query = query.Where("preparation_time <= ?", *filters.MaxPrepTime)
	}

	// Rating filter
	if filters.MinRating != nil {
		query = query.Where("rating >= ?", *filters.MinRating)
	}

	// Allergen exclusion filter
	if len(filters.Allergens) > 0 {
		for _, allergen := range filters.Allergens {
			query = query.Where("NOT (allergens @> ?)", fmt.Sprintf(`["%s"]`, allergen))
		}
	}

	return query
}

func (r *MenuRepository) applyMenuSortingAndPagination(query *gorm.DB, filters types.CustomerFilters) *gorm.DB {
	// Apply sorting
	sortBy := filters.SortBy
	if sortBy == "" {
		sortBy = "menu_items.is_popular DESC, menu_items.rating DESC, menu_items.name"
	}

	sortOrder := strings.ToUpper(filters.SortOrder)
	if sortOrder != "ASC" && sortOrder != "DESC" {
		sortOrder = "ASC"
	}

	// Add table prefix to common column names if not already prefixed
	if !strings.Contains(sortBy, ".") && !strings.Contains(sortBy, "DESC") && !strings.Contains(sortBy, "ASC") {
		// Handle specific column mappings
		switch sortBy {
		case "created_at", "updated_at", "name", "price", "rating", "is_popular", "is_available":
			sortBy = fmt.Sprintf("menu_items.%s", sortBy)
		default:
			sortBy = fmt.Sprintf("menu_items.%s", sortBy)
		}
	}

	if !strings.Contains(sortBy, "DESC") && !strings.Contains(sortBy, "ASC") {
		query = query.Order(fmt.Sprintf("%s %s", sortBy, sortOrder))
	} else {
		query = query.Order(sortBy)
	}

	// Apply pagination
	if filters.Page > 0 && filters.Limit > 0 {
		offset := (filters.Page - 1) * filters.Limit
		query = query.Offset(offset).Limit(filters.Limit)
	}

	return query
}

// parseCustomizationsFromOptions parses customizations from the JSONB options field
func (r *MenuRepository) parseCustomizationsFromOptions(ctx context.Context, itemID uuid.UUID) ([]types.MenuItemCustomization, bool) {

	// Get the menu item with options field using a struct
	var menuItem struct {
		ID      uuid.UUID      `gorm:"column:id"`
		Options datatypes.JSON `gorm:"column:options"`
	}

	err := r.db.WithContext(ctx).
		Table("menu_items").
		Select("id, options").
		Where("id = ?", itemID).
		First(&menuItem).Error

	if err != nil {
		r.logger.Error("Failed to get menu item options", "error", err, "item_id", itemID)
		return nil, false
	}

	// If options is empty or null, no customizations
	if len(menuItem.Options) == 0 || string(menuItem.Options) == "null" || string(menuItem.Options) == "[]" {
		return nil, false
	}

	// Parse the JSONB options into merchant backend format
	var merchantOptions []MerchantMenuItemOption
	if err := json.Unmarshal(menuItem.Options, &merchantOptions); err != nil {
		r.logger.Error("Failed to parse menu item options", "error", err, "item_id", itemID, "options", string(menuItem.Options))
		return nil, false
	}

	// If no options, no customizations
	if len(merchantOptions) == 0 {
		return nil, false
	}

	// Convert merchant format to customer format
	customizations := make([]types.MenuItemCustomization, len(merchantOptions))
	for i, option := range merchantOptions {
		// Convert choices to customer format
		customerOptions := make([]types.MenuItemCustomizationOption, len(option.Choices))
		for j, choice := range option.Choices {
			customerOptions[j] = types.MenuItemCustomizationOption{
				ID:          choice.ID,
				Name:        choice.Name,
				PriceChange: choice.Price,
				IsAvailable: true, // Assume available if in the options
			}
		}

		customizations[i] = types.MenuItemCustomization{
			ID:       option.ID,
			Name:     option.Name,
			Type:     option.Type,
			Required: option.Required,
			Options:  customerOptions,
		}
	}

	return customizations, true
}

// MerchantMenuItemOption represents the merchant backend's option structure
type MerchantMenuItemOption struct {
	ID       uuid.UUID                `json:"id"`
	Name     string                   `json:"name"`
	Type     string                   `json:"type"`
	Required bool                     `json:"required"`
	Choices  []MerchantMenuItemChoice `json:"choices"`
}

// MerchantMenuItemChoice represents the merchant backend's choice structure
type MerchantMenuItemChoice struct {
	ID        uuid.UUID `json:"id"`
	OptionID  uuid.UUID `json:"option_id"`
	Name      string    `json:"name"`
	Price     float64   `json:"price"`
	IsDefault bool      `json:"is_default"`
}
